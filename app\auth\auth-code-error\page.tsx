'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export default function AuthCodeError() {
  const router = useRouter()

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6 text-center">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-red-600">Authentication Error</h1>
          <p className="text-gray-600">
            There was an error during the authentication process. This could be due to:
          </p>
          <ul className="text-left text-sm text-gray-500 space-y-1">
            <li>• Invalid or expired authentication code</li>
            <li>• Network connectivity issues</li>
            <li>• Supabase configuration problems</li>
          </ul>
        </div>
        
        <div className="space-y-3">
          <Button 
            onClick={() => router.push('/app')} 
            className="w-full"
          >
            Try Again
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => router.push('/')} 
            className="w-full"
          >
            Go Home
          </Button>
        </div>
      </div>
    </div>
  )
}
