"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface NetworkRequest {
  id: string;
  url: string;
  method: string;
  status: number;
  timestamp: Date;
  duration?: number;
  error?: string;
  headers?: Record<string, string>;
}

const NetworkDebug = () => {
  const [requests, setRequests] = useState<NetworkRequest[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (!isMonitoring) return;

    // Intercept fetch requests
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const [url, options] = args;
      const requestId = Math.random().toString(36).substr(2, 9);
      const startTime = Date.now();
      
      const request: NetworkRequest = {
        id: requestId,
        url: url.toString(),
        method: options?.method || 'GET',
        status: 0,
        timestamp: new Date(),
      };

      try {
        const response = await originalFetch(...args);
        const endTime = Date.now();
        
        setRequests(prev => [...prev, {
          ...request,
          status: response.status,
          duration: endTime - startTime,
        }]);
        
        return response;
      } catch (error: any) {
        const endTime = Date.now();
        
        setRequests(prev => [...prev, {
          ...request,
          status: 0,
          duration: endTime - startTime,
          error: error.message,
        }]);
        
        throw error;
      }
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, [isMonitoring]);

  const clearRequests = () => {
    setRequests([]);
  };

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
    if (!isMonitoring) {
      clearRequests();
    }
  };

  const getStatusColor = (status: number) => {
    if (status === 0) return "destructive";
    if (status >= 200 && status < 300) return "default";
    if (status >= 400) return "destructive";
    return "secondary";
  };

  const supabaseRequests = requests.filter(req => 
    req.url.includes('supabase.co') || req.url.includes('/auth/')
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>🌐 Network Monitor</span>
          <div className="flex gap-2">
            <Button 
              onClick={toggleMonitoring} 
              variant={isMonitoring ? "destructive" : "default"}
              size="sm"
            >
              {isMonitoring ? "Stop Monitoring" : "Start Monitoring"}
            </Button>
            <Button onClick={clearRequests} variant="outline" size="sm">
              Clear
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-4 text-sm">
            <div>Total Requests: <Badge>{requests.length}</Badge></div>
            <div>Supabase Requests: <Badge>{supabaseRequests.length}</Badge></div>
            <div>Status: <Badge variant={isMonitoring ? "default" : "secondary"}>
              {isMonitoring ? "Monitoring" : "Stopped"}
            </Badge></div>
          </div>

          <ScrollArea className="h-96 w-full">
            <div className="space-y-2">
              {requests.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {isMonitoring ? "No requests captured yet..." : "Click 'Start Monitoring' to begin"}
                </div>
              ) : (
                requests.slice().reverse().map((request) => (
                  <div key={request.id} className="border rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{request.method}</Badge>
                        <Badge variant={getStatusColor(request.status)}>
                          {request.status || 'ERROR'}
                        </Badge>
                        {request.duration && (
                          <Badge variant="secondary">{request.duration}ms</Badge>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {request.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className="text-sm font-mono break-all">
                      {request.url}
                    </div>
                    
                    {request.error && (
                      <div className="text-red-500 text-xs">
                        Error: {request.error}
                      </div>
                    )}
                    
                    {request.url.includes('supabase.co') && (
                      <Badge variant="outline" className="text-xs">
                        Supabase Request
                      </Badge>
                    )}
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
};

export default NetworkDebug;
