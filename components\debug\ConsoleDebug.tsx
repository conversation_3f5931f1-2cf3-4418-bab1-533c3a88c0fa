"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ConsoleLog {
  id: string;
  type: 'log' | 'error' | 'warn' | 'info';
  message: string;
  timestamp: Date;
  args: any[];
}

const ConsoleDebug = () => {
  const [logs, setLogs] = useState<ConsoleLog[]>([]);
  const [isCapturing, setIsCapturing] = useState(false);

  useEffect(() => {
    if (!isCapturing) return;

    // Store original console methods
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
    };

    const createLogCapture = (type: ConsoleLog['type']) => {
      return (...args: any[]) => {
        // Call original method
        originalConsole[type](...args);
        
        // Capture the log
        const logEntry: ConsoleLog = {
          id: Math.random().toString(36).substr(2, 9),
          type,
          message: args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '),
          timestamp: new Date(),
          args,
        };
        
        setLogs(prev => [...prev.slice(-99), logEntry]); // Keep last 100 logs
      };
    };

    // Override console methods
    console.log = createLogCapture('log');
    console.error = createLogCapture('error');
    console.warn = createLogCapture('warn');
    console.info = createLogCapture('info');

    return () => {
      // Restore original console methods
      console.log = originalConsole.log;
      console.error = originalConsole.error;
      console.warn = originalConsole.warn;
      console.info = originalConsole.info;
    };
  }, [isCapturing]);

  const clearLogs = () => {
    setLogs([]);
  };

  const toggleCapturing = () => {
    setIsCapturing(!isCapturing);
    if (!isCapturing) {
      clearLogs();
    }
  };

  const getLogColor = (type: ConsoleLog['type']) => {
    switch (type) {
      case 'error': return 'destructive';
      case 'warn': return 'secondary';
      case 'info': return 'default';
      default: return 'outline';
    }
  };

  const getLogIcon = (type: ConsoleLog['type']) => {
    switch (type) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  const supabaseLogs = logs.filter(log => 
    log.message.toLowerCase().includes('supabase') || 
    log.message.toLowerCase().includes('auth') ||
    log.message.toLowerCase().includes('unauthorized') ||
    log.message.toLowerCase().includes('401')
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>📋 Console Monitor</span>
          <div className="flex gap-2">
            <Button 
              onClick={toggleCapturing} 
              variant={isCapturing ? "destructive" : "default"}
              size="sm"
            >
              {isCapturing ? "Stop Capturing" : "Start Capturing"}
            </Button>
            <Button onClick={clearLogs} variant="outline" size="sm">
              Clear
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-4 text-sm">
            <div>Total Logs: <Badge>{logs.length}</Badge></div>
            <div>Auth Related: <Badge>{supabaseLogs.length}</Badge></div>
            <div>Errors: <Badge variant="destructive">{logs.filter(l => l.type === 'error').length}</Badge></div>
            <div>Status: <Badge variant={isCapturing ? "default" : "secondary"}>
              {isCapturing ? "Capturing" : "Stopped"}
            </Badge></div>
          </div>

          <ScrollArea className="h-96 w-full">
            <div className="space-y-2">
              {logs.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {isCapturing ? "No logs captured yet..." : "Click 'Start Capturing' to begin"}
                </div>
              ) : (
                logs.slice().reverse().map((log) => (
                  <div key={log.id} className="border rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span>{getLogIcon(log.type)}</span>
                        <Badge variant={getLogColor(log.type)}>
                          {log.type.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">
                        {log.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className="text-sm font-mono whitespace-pre-wrap break-all bg-gray-50 p-2 rounded">
                      {log.message}
                    </div>
                    
                    {(log.message.toLowerCase().includes('supabase') || 
                      log.message.toLowerCase().includes('auth') ||
                      log.message.toLowerCase().includes('unauthorized')) && (
                      <Badge variant="outline" className="text-xs">
                        Auth Related
                      </Badge>
                    )}
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConsoleDebug;
