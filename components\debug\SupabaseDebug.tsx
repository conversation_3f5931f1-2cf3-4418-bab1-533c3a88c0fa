"use client";

import { useState, useEffect } from "react";
import { useSupabaseClient, useSessionContext } from "@supabase/auth-helpers-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const SupabaseDebug = () => {
  const supabase = useSupabaseClient();
  const { session, isLoading } = useSessionContext();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<any>({});

  useEffect(() => {
    // Collect debug information
    const info = {
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      anonKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
      anonKeyFormat: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.split('.').length === 3 ? 'Valid JWT' : 'Invalid JWT',
      sessionExists: !!session,
      sessionLoading: isLoading,
      userExists: !!session?.user,
      userId: session?.user?.id || 'None',
      userEmail: session?.user?.email || 'None',
      accessToken: session?.access_token ? 'Present' : 'Missing',
      refreshToken: session?.refresh_token ? 'Present' : 'Missing',
    };
    setDebugInfo(info);
  }, [session, isLoading]);

  const testConnection = async () => {
    const results: any = {};
    
    try {
      // Test 1: Basic client creation
      results.clientCreated = !!supabase;
      
      // Test 2: Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      results.getUserTest = {
        success: !userError,
        error: userError?.message,
        hasUser: !!userData?.user
      };

      // Test 3: Test database connection
      const { data: dbData, error: dbError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      results.databaseTest = {
        success: !dbError,
        error: dbError?.message,
        data: dbData
      };

      // Test 4: Test auth session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      results.sessionTest = {
        success: !sessionError,
        error: sessionError?.message,
        hasSession: !!sessionData?.session
      };

    } catch (error: any) {
      results.generalError = error.message;
    }

    setTestResults(results);
  };

  const clearSession = async () => {
    await supabase.auth.signOut();
    setTestResults({});
  };

  return (
    <div className="space-y-4 p-4 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔍 Supabase Debug Panel
            <Badge variant={debugInfo.sessionExists ? "default" : "destructive"}>
              {debugInfo.sessionExists ? "Authenticated" : "Not Authenticated"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Environment Variables */}
          <div>
            <h3 className="font-semibold mb-2">Environment Variables</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Supabase URL: <Badge variant={debugInfo.supabaseUrl ? "default" : "destructive"}>{debugInfo.supabaseUrl || "Missing"}</Badge></div>
              <div>Anon Key: <Badge variant={debugInfo.hasAnonKey ? "default" : "destructive"}>{debugInfo.hasAnonKey ? "Present" : "Missing"}</Badge></div>
              <div>Key Length: <span className="font-mono">{debugInfo.anonKeyLength}</span></div>
              <div>Key Format: <Badge variant={debugInfo.anonKeyFormat === 'Valid JWT' ? "default" : "destructive"}>{debugInfo.anonKeyFormat}</Badge></div>
            </div>
          </div>

          {/* Session Information */}
          <div>
            <h3 className="font-semibold mb-2">Session Information</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Session Loading: <Badge variant={debugInfo.sessionLoading ? "secondary" : "default"}>{debugInfo.sessionLoading ? "Yes" : "No"}</Badge></div>
              <div>Session Exists: <Badge variant={debugInfo.sessionExists ? "default" : "destructive"}>{debugInfo.sessionExists ? "Yes" : "No"}</Badge></div>
              <div>User Exists: <Badge variant={debugInfo.userExists ? "default" : "destructive"}>{debugInfo.userExists ? "Yes" : "No"}</Badge></div>
              <div>User ID: <span className="font-mono text-xs">{debugInfo.userId}</span></div>
              <div>User Email: <span className="font-mono text-xs">{debugInfo.userEmail}</span></div>
              <div>Access Token: <Badge variant={debugInfo.accessToken === 'Present' ? "default" : "destructive"}>{debugInfo.accessToken}</Badge></div>
            </div>
          </div>

          {/* Test Results */}
          {Object.keys(testResults).length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Test Results</h3>
              <div className="space-y-2 text-sm">
                {testResults.clientCreated !== undefined && (
                  <div>Client Created: <Badge variant={testResults.clientCreated ? "default" : "destructive"}>{testResults.clientCreated ? "✅" : "❌"}</Badge></div>
                )}
                
                {testResults.getUserTest && (
                  <div>Get User Test: 
                    <Badge variant={testResults.getUserTest.success ? "default" : "destructive"}>
                      {testResults.getUserTest.success ? "✅" : "❌"}
                    </Badge>
                    {testResults.getUserTest.error && <span className="text-red-500 ml-2">{testResults.getUserTest.error}</span>}
                  </div>
                )}
                
                {testResults.databaseTest && (
                  <div>Database Test: 
                    <Badge variant={testResults.databaseTest.success ? "default" : "destructive"}>
                      {testResults.databaseTest.success ? "✅" : "❌"}
                    </Badge>
                    {testResults.databaseTest.error && <span className="text-red-500 ml-2">{testResults.databaseTest.error}</span>}
                  </div>
                )}
                
                {testResults.sessionTest && (
                  <div>Session Test: 
                    <Badge variant={testResults.sessionTest.success ? "default" : "destructive"}>
                      {testResults.sessionTest.success ? "✅" : "❌"}
                    </Badge>
                    {testResults.sessionTest.error && <span className="text-red-500 ml-2">{testResults.sessionTest.error}</span>}
                  </div>
                )}
                
                {testResults.generalError && (
                  <div className="text-red-500">General Error: {testResults.generalError}</div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button onClick={testConnection} variant="outline">
              Run Connection Tests
            </Button>
            {debugInfo.sessionExists && (
              <Button onClick={clearSession} variant="destructive">
                Clear Session
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupabaseDebug;
