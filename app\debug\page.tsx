"use client";

import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DebugPage() {
  const supabase = useSupabaseClient();

  const triggerAuthTest = async () => {
    console.log("🔍 Testing Supabase auth...");
    const { data, error } = await supabase.auth.getUser();
    console.log("Auth result:", { data, error });
  };

  const triggerDatabaseTest = async () => {
    console.log("🔍 Testing database connection...");
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    console.log("Database result:", { data, error });
  };

  const triggerNetworkTest = async () => {
    console.log("🔍 Testing network request...");
    try {
      const response = await fetch('/api/test-endpoint');
      console.log("Network test result:", response.status);
    } catch (error) {
      console.error("Network test error:", error);
    }
  };

  const triggerError = () => {
    console.error("🚨 This is a test error message");
    console.warn("⚠️ This is a test warning message");
    console.info("ℹ️ This is a test info message");
  };

  return (
    <div className="container mx-auto p-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🐛 Debug Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            Use this page to test the debug components. Click the red "Debug Panel" button 
            in the bottom right corner to open the debug dashboard.
          </p>
          
          <div className="grid grid-cols-2 gap-4">
            <Button onClick={triggerAuthTest} variant="outline">
              Test Auth
            </Button>
            <Button onClick={triggerDatabaseTest} variant="outline">
              Test Database
            </Button>
            <Button onClick={triggerNetworkTest} variant="outline">
              Test Network
            </Button>
            <Button onClick={triggerError} variant="outline">
              Trigger Logs
            </Button>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded">
            <h3 className="font-semibold mb-2">How to use the Debug Dashboard:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Click the red "🐛 Debug Panel" button in the bottom right</li>
              <li>Use the "Supabase" tab to check your connection and environment variables</li>
              <li>Use the "Network" tab to monitor API calls (click "Start Monitoring" first)</li>
              <li>Use the "Console" tab to capture console logs (click "Start Capturing" first)</li>
              <li>Use the "Overview" tab for common issues and solutions</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
