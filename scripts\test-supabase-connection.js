// Test script to verify Supabase connection
// Run with: node scripts/test-supabase-connection.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection...\n');
  
  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  console.log('Environment Variables:');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Set' : '❌ Missing');
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('\n❌ Missing required environment variables');
    return;
  }
  
  // Check if anon key looks like a valid JWT
  const keyParts = supabaseAnonKey.split('.');
  console.log('Anon Key Format:', keyParts.length === 3 ? '✅ Valid JWT format' : '❌ Invalid JWT format');
  
  if (keyParts.length !== 3) {
    console.log('❌ The anon key appears to be truncated or invalid');
    return;
  }
  
  try {
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test connection by trying to get auth user (will be null but shouldn't error)
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error && error.message.includes('Invalid JWT')) {
      console.log('\n❌ Invalid JWT token - please check your anon key');
      return;
    }
    
    console.log('\n✅ Supabase client created successfully');
    console.log('Current user:', user ? 'Authenticated' : 'Not authenticated (expected)');
    
    // Test database connection
    const { data, error: dbError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
      
    if (dbError) {
      console.log('❌ Database connection error:', dbError.message);
    } else {
      console.log('✅ Database connection successful');
    }
    
  } catch (error) {
    console.log('\n❌ Connection failed:', error.message);
  }
}

testSupabaseConnection();
