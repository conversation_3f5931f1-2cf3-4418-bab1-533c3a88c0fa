# 🔧 Supabase Setup Guide

## Step 1: Get Your Supabase Keys

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `enfekksfmsndgriehlsc`
3. Navigate to **Settings** → **API**
4. Copy these keys:
   - **Project URL**: `https://enfekksfmsndgriehlsc.supabase.co`
   - **anon public key**: (This should be a long JWT token starting with `eyJ`)
   - **service_role key**: (For server-side operations)

## Step 2: Update .env.local

Replace the placeholder in your `.env.local` file:

```bash
NEXT_PUBLIC_SUPABASE_URL=https://enfekksfmsndgriehlsc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.YOUR_ACTUAL_PAYLOAD_HERE.YOUR_ACTUAL_SIGNATURE_HERE
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 3: Configure Google OAuth

1. In Supabase Dashboard, go to **Authentication** → **Providers**
2. Enable **Google** provider
3. Add your Google OAuth credentials:
   - Client ID from Google Cloud Console
   - Client Secret from Google Cloud Console

## Step 4: Set Redirect URLs

### In Supabase:
1. Go to **Authentication** → **Settings**
2. Add these URLs to **Site URL**:
   - `http://localhost:3000` (for development)
   - Your production domain (if deployed)
3. Add these to **Redirect URLs**:
   - `http://localhost:3000/auth/callback`
   - Your production callback URL

### In Google Cloud Console:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services** → **Credentials**
3. Edit your OAuth 2.0 Client ID
4. Add these to **Authorized redirect URIs**:
   - `https://enfekksfmsndgriehlsc.supabase.co/auth/v1/callback`

## Step 5: Test Your Setup

1. Run the test script:
   ```bash
   node scripts/test-supabase-connection.js
   ```

2. Check the debug panel in your app:
   - Should show "Valid JWT format"
   - Environment variables should all be "Present"

## Step 6: Restart Your Development Server

After updating environment variables:
```bash
# Stop current server (Ctrl+C)
npm run dev
```

## Common Issues:

### Invalid JWT Format
- Your anon key is truncated or incomplete
- Get the complete key from Supabase dashboard

### 401 Unauthorized
- Wrong anon key
- Google OAuth not configured properly
- Redirect URLs don't match

### Database Connection Issues
- RLS policies not set correctly
- Service role key missing for server operations

## Verification Checklist:

- [ ] Anon key is complete JWT (3 parts separated by dots)
- [ ] Anon key starts with "eyJ"
- [ ] Google OAuth enabled in Supabase
- [ ] Redirect URLs configured in both Supabase and Google
- [ ] Development server restarted after env changes
- [ ] Debug panel shows "Valid JWT format"
