{"name": "text-behind-image", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@imgly/background-removal": "^1.5.5", "@imgly/background-removal-node": "^1.4.5", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@tabler/icons-react": "^3.17.0", "@vercel/analytics": "^1.3.1", "@vercel/speed-insights": "^1.0.12", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cobe": "^0.6.3", "framer-motion": "^11.7.0", "geist": "^1.3.1", "lucide-react": "^0.439.0", "mini-svg-data-uri": "^1.4.4", "next": "^15.3.4", "next-themes": "^0.4.3", "random-word-slugs": "^0.1.7", "react": "^18", "react-color": "^2.19.3", "react-dom": "^18", "react-icons": "^5.3.0", "react-switch": "^7.1.0", "stripe": "^17.3.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-color": "^3.0.12", "@types/react-dom": "^18", "postcss": "^8", "supabase": "^1.191.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}