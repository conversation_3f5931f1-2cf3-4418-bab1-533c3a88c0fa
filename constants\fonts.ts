export const FREE_FONTS = [
    '<PERSON>',
    'Playfair Display',
    'Dancing Script',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>ather',
    'Pacifico'
];

export const ALL_FONTS = [
    "<PERSON>ee<PERSON><PERSON>", "<PERSON>", "Abril Fatface", "Acme", "Alata", "Albert Sans", "Alegreya", "Alegreya Sans",
    "Alegreya Sans SC", "Alfa Slab One", "Alice", "Almarai", "Amatic SC", "Amiri", "Antic Slab", "Anton",
    "Architects Daughter", "Archivo", "Archivo Black", "Archivo Narrow", "Arimo", "Arsenal", "Arvo", "Asap",
    "Asap Condensed", "Assistant", "Barlow", "Barlow Condensed", "Barlow Semi Condensed", "Be Vietnam Pro",
    "Bebas Neue", "Bitter", "Black Ops One", "Bodoni Moda", "Bree Serif", "Bungee", "Cabin", "Cairo", "Cantarell",
    "Cardo", "Catamaran", "Caveat", "<PERSON><PERSON> Petch", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Comfortaa", "Commissioner",
    "Concert One", "<PERSON><PERSON>", "Cormorant", "Cormorant Garamond", "Courgette", "Crete Round", "Crimson Pro",
    "Crimson Text", "Cuprum", "DM Sans", "DM Serif Display", "DM Serif Text", "Dancing Script", "Didact Gothic",
    "Domine", "Dosis", "EB Garamond", "Eczar", "El Messiri", "Electrolize", "Encode Sans", "Encode Sans Condensed",
    "Exo", "Exo 2", "Figtree", "Fira Sans", "Fira Sans Condensed", "Fjalla One", "Francois One", "Frank Ruhl Libre",
    "Fraunces", "Gelasio", "Gloria Hallelujah", "Gothic A1", "Great Vibes", "Gruppo", "Heebo", "Hind", "Hind Madurai",
    "Hind Siliguri", "IBM Plex Mono", "IBM Plex Sans", "IBM Plex Sans Arabic", "IBM Plex Sans Condensed", "IBM Plex Serif",
    "Inconsolata", "Indie Flower", "Inter", "Inter Tight", "Josefin Sans", "Josefin Slab", "Jost", "Kalam", "Kanit",
    "Karla", "Kaushan Script", "Khand", "Lato", "League Spartan", "Lexend", "Lexend Deca", "Libre Barcode 39",
    "Libre Baskerville", "Libre Caslon Text", "Libre Franklin", "Lilita One", "Lobster", "Lobster Two", "Lora",
    "Luckiest Guy", "M PLUS 1p", "M PLUS Rounded 1c", "Macondo", "Manrope", "Marcellus", "Martel", "Mate", "Mate SC",
    "Maven Pro", "Merienda", "Merriweather", "Merriweather Sans", "Montserrat", "Montserrat Alternates", "Mukta",
    "Mulish", "Nanum Gothic", "Nanum Gothic Coding", "Nanum Myeongjo", "Neuton", "Noticia Text", "Noto Color Emoji",
    "Noto Kufi Arabic", "Noto Naskh Arabic", "Noto Sans", "Noto Sans Arabic", "Noto Sans Bengali", "Noto Sans Display",
    "Noto Sans HK", "Noto Sans JP", "Noto Sans KR", "Noto Sans Mono", "Noto Sans SC", "Noto Sans TC", "Noto Sans Thai",
    "Noto Serif", "Noto Serif JP", "Noto Serif KR", "Noto Serif TC", "Nunito", "Nunito Sans", "Old Standard TT",
    "Oleo Script", "Open Sans", "Orbitron", "Oswald", "Outfit", "Overpass", "Oxygen", "PT Sans", "PT Sans Caption",
    "PT Sans Narrow", "PT Serif", "Pacifico", "Passion One", "Pathway Gothic One", "Patua One", "Paytone One",
    "Permanent Marker", "Philosopher", "Play", "Playfair Display", "Plus Jakarta Sans", "Poppins", "Prata", "Prompt",
    "Public Sans", "Quattrocento", "Quattrocento Sans", "Questrial", "Quicksand", "Rajdhani", "Raleway", "Readex Pro", "Red Hat Display", "Righteous",
    "Roboto", "Roboto Condensed", "Roboto Flex", "Roboto Mono", "Roboto Serif", "Roboto Slab",
    "Rokkitt", "Rowdies", "Rubik", "Rubik Bubbles", "Rubik Mono One", "Russo One",
    "Sacramento", "Saira", "Saira Condensed", "Sarabun", "Satisfy", "Sawarabi Gothic",
    "Sawarabi Mincho", "Sen", "Shadows Into Light", "Signika", "Signika Negative",
    "Silkscreen", "Six Caps", "Slabo 27px", "Sora", "Source Code Pro", "Source Sans 3",
    "Source Serif 4", "Space Grotesk", "Space Mono", "Special Elite", "Spectral",
    "Tajawal", "Tangerine", "Teko", "Tinos", "Titan One", "Titillium Web",
    "Ubuntu", "Ubuntu Condensed", "Ubuntu Mono", "Unbounded", "Unna", "Urbanist",
    "Varela Round", "Vollkorn", "Work Sans", "Yanone Kaffeesatz", "Yantramanav",
    "Yellowtail", "Yeseva One", "Zen Kaku Gothic New", "Zeyada", "Zilla Slab"
]